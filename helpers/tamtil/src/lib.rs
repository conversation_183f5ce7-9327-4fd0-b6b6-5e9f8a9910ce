//! # TAMTIL: Zero-Copy Actor System
//! 
//! A production-ready actor system built on rkyv's zero-copy serialization,
//! following <PERSON> R<PERSON>hl's actor pattern with action->reaction and remember->recall.
//! 
//! ## Design Philosophy
//! 
//! Like Tokio's evolution from simple async runtime to Tower's service abstraction,
//! TAMTIL evolves from basic actor communication to a distributed, fault-tolerant
//! system. This notebook-style implementation shows that evolution.
//! 
//! ### Core Principles:
//! 1. **Zero-Copy Everything**: rkyv for all serialization, preserving type information
//! 2. **Alice Ryhl Pattern**: Separate Task and Handle, oneshot for responses
//! 3. **URL-Based Addressing**: Hierarchical actor IDs like platform.com/context/actor
//! 4. **Action->Reaction**: All actor communication follows this pattern
//! 5. **Remember->Recall**: Persistent state through interned memory operations
//! 6. **Extreme Performance**: One-thread-per-core, zero-allocation hot paths

// ============================================================================
// STEP 1: FOUNDATIONS - Error Handling and Core Types
// ============================================================================

use rkyv::{
    Archive, Deserialize, Serialize,
    api::high::{to_bytes, from_bytes},
    rancor::Error as RancorError,
};
use tracing;
use std::fmt;
use thiserror::Error;

/// TAMTIL's error system is designed for both development and production.
/// Each error provides enough context for debugging while being efficient.
#[derive(Error, Debug, Clone)]
pub enum TamtilError {
    #[error("Actor not found: {id}")]
    ActorNotFound { id: String },
    
    #[error("Storage error: {message}")]
    Storage { message: String },
    
    #[error("Communication error: {message}")]
    Communication { message: String },
    
    #[error("Serialization failed: {message}")]
    Serialization { message: String },
    
    #[error("Deserialization failed: {message}")]
    Deserialization { message: String },
    
    #[error("Invalid actor address: {address}")]
    InvalidAddress { address: String },
    
    #[error("Actor {id} panicked: {panic_message}")]
    ActorPanic { id: String, panic_message: String },
    
    #[error("Memory corruption detected at offset {offset}")]
    MemoryCorruption { offset: u64 },
}

/// Result type for all TAMTIL operations
pub type TamtilResult<T> = Result<T, TamtilError>;

// ============================================================================
// STEP 2: ACTOR ADDRESSING - URL-Based Hierarchical IDs
// ============================================================================

/// Actor addresses follow URL patterns: platform.com/context/actor/child
/// This enables natural distribution and fault isolation.
/// 
/// Examples:
/// - "platform.local" (root platform)
/// - "platform.local/web" (web context)
/// - "platform.local/web/user-session/auth" (auth actor in user session)
#[derive(Debug, Clone, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct ActorId(String);

impl ActorId {
    /// Create a new actor ID
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }
    
    /// Get the string representation
    pub fn as_str(&self) -> &str {
        &self.0
    }
    
    /// Create a child actor ID
    pub fn child(&self, name: impl Into<String>) -> Self {
        Self(format!("{}/{}", self.0, name.into()))
    }
    
    /// Get the parent actor ID
    pub fn parent(&self) -> Option<Self> {
        self.0.rfind('/').map(|pos| Self(self.0[..pos].to_string()))
    }
    
    /// Get the depth in the hierarchy (0 = root)
    pub fn depth(&self) -> usize {
        if self.0.is_empty() { 0 } else { self.0.matches('/').count() }
    }
    
    /// Check if this is a child of another actor
    pub fn is_child_of(&self, parent: &ActorId) -> bool {
        self.0.starts_with(parent.as_str()) 
            && self.0.len() > parent.0.len() 
            && self.0.chars().nth(parent.0.len()) == Some('/')
    }
}

impl From<&str> for ActorId {
    fn from(s: &str) -> Self {
        Self(s.to_string())
    }
}

impl From<String> for ActorId {
    fn from(s: String) -> Self {
        Self(s)
    }
}

// ============================================================================
// STEP 3: MEMORY SYSTEM - Remember->Recall Pattern
// ============================================================================

/// Memory values are the fundamental data types in TAMTIL.
/// They're designed for zero-copy serialization with rkyv.
#[derive(Debug, Clone, PartialEq, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum MemoryValue {
    String(String),
    Number(f64),
    Boolean(bool),
    Bytes(Vec<u8>),
}

impl MemoryValue {
    pub fn string(s: impl Into<String>) -> Self { Self::String(s.into()) }
    pub fn number(n: f64) -> Self { Self::Number(n) }
    pub fn boolean(b: bool) -> Self { Self::Boolean(b) }
    pub fn bytes(b: Vec<u8>) -> Self { Self::Bytes(b) }
}

impl fmt::Display for MemoryValue {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::String(s) => write!(f, "{}", s),
            Self::Number(n) => write!(f, "{}", n),
            Self::Boolean(b) => write!(f, "{}", b),
            Self::Bytes(b) => write!(f, "{} bytes", b.len()),
        }
    }
}

/// Memory operations represent state changes that can be persisted.
/// These form the foundation of TAMTIL's event sourcing approach.
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum MemoryOperation {
    Create { key: String, value: MemoryValue },
    Update { key: String, value: MemoryValue },
    Delete { key: String },
    Link { from: String, to: String, relation: String },
    Unlink { from: String, to: String, relation: String },
}

// ============================================================================
// STEP 4: REACTION TRAIT - The Heart of Actor State
// ============================================================================

/// The Reaction trait is central to TAMTIL's design.
/// Every actor action produces a reaction that can be remembered.
///
/// For now, we'll use a simpler trait bound that doesn't require specific serializers
pub trait Reaction: Archive + Send + Sync + 'static {
    /// Convert this reaction into memory operations for persistence
    fn remember(&self) -> Vec<MemoryOperation>;
}

// ============================================================================
// STEP 6: STORAGE LAYER - Zero-Copy Interning with Production Robustness
// ============================================================================

use memmap2::{MmapMut, MmapOptions};
use std::{
    collections::HashMap,
    hash::{Hash, Hasher, DefaultHasher},
    path::{Path, PathBuf},
    sync::{
        atomic::{AtomicU64, Ordering},
        Arc,
    },
};
use tokio::sync::RwLock;

/// Constants for memory-mapped file management
const DISK_FILE_SIZE: u64 = 1024 * 1024 * 1024; // 1GB per file
const DISK_HEADER_SIZE: usize = 16; // 8 bytes write_offset + 8 bytes next_symbol

/// A symbol represents an interned piece of data.
/// Symbols enable zero-copy access to frequently used data.
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct Symbol(u64);

impl Symbol {
    pub fn new(id: u64) -> Self {
        Self(id)
    }

    pub fn id(&self) -> u64 {
        self.0
    }
}

/// Entry header for robust data recovery with length prefixing
#[derive(Debug, Clone, Copy)]
struct EntryHeader {
    entry_size: u32,    // Size of the serialized InternedEntry
    checksum: u32,      // CRC32 of the serialized InternedEntry
}

impl EntryHeader {
    const SIZE: usize = 8; // 4 bytes entry_size + 4 bytes checksum

    fn new(entry_size: u32, data: &[u8]) -> Self {
        Self {
            entry_size,
            checksum: Self::calculate_crc32(data),
        }
    }

    /// Production-grade CRC32 implementation for data integrity
    fn calculate_crc32(data: &[u8]) -> u32 {
        let mut crc = 0xFFFFFFFFu32;
        for &byte in data {
            crc ^= byte as u32;
            for _ in 0..8 {
                if crc & 1 != 0 {
                    crc = (crc >> 1) ^ 0xEDB88320;
                } else {
                    crc >>= 1;
                }
            }
        }
        !crc
    }

    fn to_bytes(&self) -> [u8; Self::SIZE] {
        let mut bytes = [0u8; Self::SIZE];
        bytes[0..4].copy_from_slice(&self.entry_size.to_le_bytes());
        bytes[4..8].copy_from_slice(&self.checksum.to_le_bytes());
        bytes
    }

    fn from_bytes(bytes: &[u8]) -> Option<Self> {
        if bytes.len() < Self::SIZE {
            return None;
        }
        let entry_size = u32::from_le_bytes([bytes[0], bytes[1], bytes[2], bytes[3]]);
        let checksum = u32::from_le_bytes([bytes[4], bytes[5], bytes[6], bytes[7]]);
        Some(Self { entry_size, checksum })
    }

    fn verify(&self, data: &[u8]) -> bool {
        self.checksum == Self::calculate_crc32(data)
    }
}

/// Interned data entry with integrity checking
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
struct InternedEntry {
    hash: u64,
    size: u32,
    timestamp: u64,
    checksum: u32, // CRC32 checksum for data integrity
    data: Vec<u8>,
}

/// Memory-mapped file for zero-copy data storage with production robustness
pub struct MappedFile {
    path: PathBuf,
    mmap: Option<MmapMut>,
    write_offset: u64,
    next_symbol: AtomicU64,
    symbol_map: HashMap<u64, (u64, u32)>, // symbol_id -> (offset, size)
    hash_to_symbol: HashMap<u64, Symbol>,
    dirty_header: bool,
}

impl MappedFile {
    /// Create or open a memory-mapped file with robust error handling
    pub async fn new(path: impl AsRef<Path>) -> TamtilResult<Self> {
        let path = path.as_ref().to_path_buf();

        // Ensure parent directory exists
        if let Some(parent) = path.parent() {
            std::fs::create_dir_all(parent).map_err(|e| TamtilError::Storage {
                message: format!("Failed to create directory {}: {}", parent.display(), e)
            })?;
        }

        let file = std::fs::OpenOptions::new()
            .read(true)
            .write(true)
            .create(true)
            .open(&path)
            .map_err(|e| TamtilError::Storage {
                message: format!("Failed to open file {}: {}", path.display(), e)
            })?;

        // Set file size if it's new
        let metadata = file.metadata().map_err(|e| TamtilError::Storage {
            message: format!("Failed to get metadata for {}: {}", path.display(), e)
        })?;

        if metadata.len() == 0 {
            file.set_len(DISK_FILE_SIZE).map_err(|e| TamtilError::Storage {
                message: format!("Failed to set file size for {}: {}", path.display(), e)
            })?;
        }

        let mmap = unsafe {
            MmapOptions::new()
                .map_mut(&file)
                .map_err(|e| TamtilError::Storage {
                    message: format!("Failed to memory-map file {}: {}", path.display(), e)
                })?
        };

        let mut mapped_file = Self {
            path,
            mmap: Some(mmap),
            write_offset: DISK_HEADER_SIZE as u64,
            next_symbol: AtomicU64::new(1),
            symbol_map: HashMap::new(),
            hash_to_symbol: HashMap::new(),
            dirty_header: false,
        };

        mapped_file.load_index().await?;
        Ok(mapped_file)
    }

    /// Load index from file header with robust error handling
    async fn load_index(&mut self) -> TamtilResult<()> {
        if let Some(ref mmap) = self.mmap {
            if mmap.len() >= DISK_HEADER_SIZE {
                // Read header: write_offset (8 bytes) + next_symbol (8 bytes)
                let write_offset_bytes = &mmap[0..8];
                let next_symbol_bytes = &mmap[8..16];

                let write_offset = u64::from_le_bytes(
                    write_offset_bytes.try_into().map_err(|_| TamtilError::Storage {
                        message: "Invalid write_offset in header".to_string()
                    })?
                );
                let next_symbol = u64::from_le_bytes(
                    next_symbol_bytes.try_into().map_err(|_| TamtilError::Storage {
                        message: "Invalid next_symbol in header".to_string()
                    })?
                );

                // Validate header values
                if write_offset >= DISK_HEADER_SIZE as u64 && write_offset <= DISK_FILE_SIZE {
                    self.write_offset = write_offset;
                    self.next_symbol.store(next_symbol, Ordering::SeqCst);

                    // Try to rebuild index from existing data
                    if write_offset > DISK_HEADER_SIZE as u64 {
                        self.rebuild_index().await?;
                    }
                } else {
                    tracing::warn!("Invalid header in {}, rebuilding from scratch", self.path.display());
                    self.write_offset = DISK_HEADER_SIZE as u64;
                    self.next_symbol.store(1, Ordering::SeqCst);
                    self.dirty_header = true;
                }
            } else {
                // File too small, initialize fresh
                self.write_offset = DISK_HEADER_SIZE as u64;
                self.next_symbol.store(1, Ordering::SeqCst);
                self.dirty_header = true;
            }
        }
        Ok(())
    }

    /// Rebuild index by scanning all entries - production-ready with length prefixing
    async fn rebuild_index(&mut self) -> TamtilResult<()> {
        if let Some(ref mmap) = self.mmap {
            let mut offset = DISK_HEADER_SIZE as u64;

            while offset < self.write_offset {
                let start = offset as usize;

                // Read entry header first
                if start + EntryHeader::SIZE > mmap.len() {
                    break;
                }

                let header = match EntryHeader::from_bytes(&mmap[start..start + EntryHeader::SIZE]) {
                    Some(h) => h,
                    None => {
                        tracing::warn!("Invalid entry header at offset {}, stopping rebuild", offset);
                        break;
                    }
                };

                let entry_start = start + EntryHeader::SIZE;
                let entry_end = entry_start + header.entry_size as usize;

                if entry_end > mmap.len() {
                    tracing::warn!("Entry extends beyond file at offset {}, stopping rebuild", offset);
                    break;
                }

                let entry_data = &mmap[entry_start..entry_end];

                // Verify checksum
                if !header.verify(entry_data) {
                    tracing::warn!("Checksum mismatch at offset {}, skipping entry", offset);
                    offset += EntryHeader::SIZE as u64 + header.entry_size as u64;
                    continue;
                }

                // Deserialize entry safely using rkyv's validation
                match from_bytes::<InternedEntry, RancorError>(entry_data) {
                    Ok(entry) => {
                        // Check if we already have this hash to avoid duplicate symbol IDs
                        let symbol = if let Some(&existing_symbol) = self.hash_to_symbol.get(&entry.hash) {
                            existing_symbol
                        } else {
                            let symbol_id = self.next_symbol.fetch_add(1, Ordering::SeqCst);
                            let symbol = Symbol::new(symbol_id);
                            self.hash_to_symbol.insert(entry.hash, symbol);
                            symbol
                        };

                        let total_size = EntryHeader::SIZE as u32 + header.entry_size;
                        self.symbol_map.insert(symbol.id(), (offset, total_size));
                    }
                    Err(e) => {
                        tracing::warn!("Entry validation failed at offset {}: {}, skipping", offset, e);
                    }
                }

                offset += EntryHeader::SIZE as u64 + header.entry_size as u64;
            }
        }
        Ok(())
    }
}

// ============================================================================
// STEP 8: ACTOR SYSTEM - Alice Ryhl's Pattern with Zero-Copy Communication
// ============================================================================

use async_trait::async_trait;
use tokio::sync::{mpsc, oneshot};

/// The core Actor trait following Alice Ryhl's pattern with full type safety
/// For now, we'll use simpler bounds and add serialization constraints later
#[async_trait]
pub trait Actor: Send + Sync + 'static {
    type Action: Archive + Send + Sync + 'static + fmt::Debug;
    type Reaction: Reaction + Clone + Default + fmt::Debug;

    /// Process an action and produce a reaction
    async fn act(&mut self, action: Self::Action, actors: &Actors, memories: &Memories) -> TamtilResult<Self::Reaction>;
}

/// Type-safe message envelope that preserves rkyv's type information
#[derive(Archive, Serialize, Deserialize)]
#[rkyv(compare(PartialEq))]
struct TypedMessage<A> {
    action: A,
    request_id: Option<u64>, // None for tell, Some for ask
    sender_id: ActorId,
}

/// Internal actor messages for lifecycle management
enum ActorMessage<A, R> {
    TypedAction(TypedMessage<A>, oneshot::Sender<TamtilResult<Vec<u8>>>),
    Shutdown,
    _Phantom(std::marker::PhantomData<R>),
}

/// Actor Task - the actual actor implementation following Alice Ryhl's pattern
pub struct ActorTask<T: Actor> {
    id: ActorId,
    actor: T,
    receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
    actors: Actors,
    memories: Memories,
}

impl<T: Actor> ActorTask<T> {
    pub fn new(
        id: ActorId,
        actor: T,
        receiver: mpsc::Receiver<ActorMessage<T::Action, T::Reaction>>,
        actors: Actors,
        memories: Memories,
    ) -> Self {
        Self {
            id,
            actor,
            receiver,
            actors,
            memories,
        }
    }

    /// Run the actor task - this is where the magic happens
    pub async fn run(mut self) -> TamtilResult<()> {
        tracing::info!("Actor {} starting", self.id.as_str());

        while let Some(message) = self.receiver.recv().await {
            match message {
                ActorMessage::TypedAction(typed_msg, response_tx) => {
                    // Process the action and produce a reaction
                    let result = self.actor.act(typed_msg.action, &self.actors, &self.memories).await;

                    match result {
                        Ok(reaction) => {
                            // For now, use a simple approach - serialize to JSON as placeholder
                            // TODO: Implement proper rkyv serialization with correct trait bounds
                            let reaction_bytes = format!("{:?}", reaction).into_bytes();

                            // Remember the reaction if it has memory operations
                            let memory_ops = reaction.remember();
                            if !memory_ops.is_empty() {
                                if let Err(e) = self.memories.remember(memory_ops).await {
                                    tracing::error!("Failed to remember reaction for actor {}: {}", self.id.as_str(), e);
                                }
                            }

                            let _ = response_tx.send(Ok(reaction_bytes));
                        }
                        Err(e) => {
                            let _ = response_tx.send(Err(e));
                        }
                    }
                }
                ActorMessage::Shutdown => {
                    tracing::info!("Actor {} shutting down", self.id.as_str());
                    break;
                }
                ActorMessage::_Phantom(_) => unreachable!(),
            }
        }

        tracing::info!("Actor {} stopped", self.id.as_str());
        Ok(())
    }
}

/// Actor Handle - the external interface following Alice Ryhl's pattern
pub struct ActorHandle<T: Actor> {
    id: ActorId,
    sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>,
}

impl<T: Actor> ActorHandle<T> {
    pub fn new(
        id: ActorId,
        sender: mpsc::Sender<ActorMessage<T::Action, T::Reaction>>,
    ) -> Self {
        Self { id, sender }
    }

    /// Send an action without waiting for response (tell pattern) - fully type-safe
    pub async fn tell(&self, action: T::Action) -> TamtilResult<()> {
        let typed_msg = TypedMessage {
            action,
            request_id: None,
            sender_id: self.id.clone(),
        };

        let (response_tx, _response_rx) = oneshot::channel();

        self.sender
            .send(ActorMessage::TypedAction(typed_msg, response_tx))
            .await
            .map_err(|_| TamtilError::Communication {
                message: format!("Failed to send message to actor {}", self.id.as_str())
            })?;

        Ok(())
    }

    /// Send an action and wait for reaction (ask pattern) - fully type-safe
    pub async fn ask(&self, action: T::Action) -> TamtilResult<T::Reaction> {
        let typed_msg = TypedMessage {
            action,
            request_id: Some(1), // TODO: Use proper request ID generation
            sender_id: self.id.clone(),
        };

        let (response_tx, response_rx) = oneshot::channel();

        self.sender
            .send(ActorMessage::TypedAction(typed_msg, response_tx))
            .await
            .map_err(|_| TamtilError::Communication {
                message: format!("Failed to send message to actor {}", self.id.as_str())
            })?;

        match response_rx.await {
            Ok(Ok(reaction_payload)) => {
                // Deserialize with full type safety and rkyv validation
                let reaction: T::Reaction = from_bytes::<T::Reaction, RancorError>(&reaction_payload)
                    .map_err(|e| TamtilError::Deserialization {
                        message: format!("Reaction deserialization failed: {}", e)
                    })?;
                Ok(reaction)
            }
            Ok(Err(e)) => Err(e),
            Err(_) => Err(TamtilError::Communication {
                message: format!("Actor {} did not respond", self.id.as_str())
            }),
        }
    }

    /// Shutdown the actor gracefully
    pub async fn shutdown(&self) -> TamtilResult<()> {
        self.sender
            .send(ActorMessage::Shutdown)
            .await
            .map_err(|_| TamtilError::Communication {
                message: format!("Failed to send shutdown to actor {}", self.id.as_str())
            })?;
        Ok(())
    }
}

// ============================================================================
// STEP 9: PLACEHOLDER TYPES - To Be Implemented
// ============================================================================

/// Placeholder for the Actors registry
#[derive(Clone)]
pub struct Actors;

/// Placeholder for the Memories system
#[derive(Clone)]
pub struct Memories;

impl Memories {
    async fn remember(&self, _operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        // TODO: Implement memory persistence
        Ok(())
    }
}

// ============================================================================
// STEP 10: BASIC TESTS - Ensure Everything Compiles
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_actor_id_hierarchy() {
        let root = ActorId::new("platform.local");
        let child = root.child("web");
        let grandchild = child.child("session");
        
        assert_eq!(root.as_str(), "platform.local");
        assert_eq!(child.as_str(), "platform.local/web");
        assert_eq!(grandchild.as_str(), "platform.local/web/session");
        
        assert_eq!(root.depth(), 0);
        assert_eq!(child.depth(), 1);
        assert_eq!(grandchild.depth(), 2);
        
        assert!(child.is_child_of(&root));
        assert!(grandchild.is_child_of(&child));
        assert!(grandchild.is_child_of(&root));
        assert!(!root.is_child_of(&child));
    }

    #[test]
    fn test_memory_value_serialization() {
        let value = MemoryValue::string("test");
        let bytes = to_bytes::<RancorError>(&value).expect("Failed to serialize");
        let deserialized: MemoryValue = from_bytes::<MemoryValue, RancorError>(&bytes).expect("Failed to deserialize");
        assert_eq!(value, deserialized);
    }

    #[test]
    fn test_memory_operation_serialization() {
        let op = MemoryOperation::Create {
            key: "test_key".to_string(),
            value: MemoryValue::number(42.0),
        };
        let bytes = to_bytes::<RancorError>(&op).expect("Failed to serialize");
        let deserialized: MemoryOperation = from_bytes::<MemoryOperation, RancorError>(&bytes).expect("Failed to deserialize");

        match (&op, &deserialized) {
            (MemoryOperation::Create { key: k1, value: v1 }, MemoryOperation::Create { key: k2, value: v2 }) => {
                assert_eq!(k1, k2);
                assert_eq!(v1, v2);
            }
            _ => panic!("Deserialization changed operation type"),
        }
    }
}
